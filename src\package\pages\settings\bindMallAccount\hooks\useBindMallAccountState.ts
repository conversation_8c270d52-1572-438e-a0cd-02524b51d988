import { useRouter } from '@tarojs/taro';
import { useCallback, useEffect, useState } from 'react';

import useCountdown from '@/hooks/use-countdown';
import { isPhoneNumber } from '@/utils/common';

/**
 * useBindMallAccountState Hook 返回值类型定义
 */
export interface UseBindMallAccountStateReturn {
  // 基础状态
  phone: string;
  code: string;
  isBound: boolean;
  codeSent: boolean;

  // 派生状态
  isPhoneValid: boolean;
  isCodeInputEnabled: boolean;
  isBindButtonEnabled: boolean;

  // 倒计时相关
  count: number;
  isRunning: boolean;
  startCountdown: () => void;

  // 状态更新函数
  setPhone: (phone: string) => void;
  setCode: (code: string) => void;
  setCodeSentStatus: (sent: boolean) => void;
  setBoundStatus: (bound: boolean) => void;
  clearFormData: () => void;
  switchToBindPage: () => void;
}

/**
 * 绑定商城账户状态管理Hook
 * 负责管理组件的所有状态逻辑
 */
export const useBindMallAccountState = (): UseBindMallAccountStateReturn => {
  // 基础状态
  const [phone, setPhone] = useState('');
  const [code, setCode] = useState('');
  const [isBound, setIsBound] = useState(false);
  const [codeSent, setCodeSent] = useState(false);

  // 路由参数处理
  const router = useRouter();

  // 倒计时Hook
  const { count, start, isRunning, clear } = useCountdown(60);

  // 初始化绑定状态
  useEffect(() => {
    const { isBound } = router.params;
    setIsBound(isBound === 'true');
  }, []);

  // 计算派生状态
  const isPhoneValid = isPhoneNumber(phone);
  const isCodeInputEnabled = codeSent;
  const isBindButtonEnabled = isPhoneValid && code.length === 6;

  // 清空表单数据
  const clearFormData = useCallback(() => {
    setPhone('');
    setCode('');
    setCodeSent(false);
    clear();
  }, [clear]);

  // 切换到绑定页面
  const switchToBindPage = useCallback(() => {
    clearFormData();
    setIsBound(false);
  }, [clearFormData]);

  // 设置验证码已发送状态
  const setCodeSentStatus = useCallback((sent: boolean) => {
    setCodeSent(sent);
  }, []);

  // 设置绑定状态
  const setBoundStatus = useCallback(
    (bound: boolean) => {
      setIsBound(bound);
      if (bound) {
        clearFormData();
      }
    },
    [clearFormData],
  );

  return {
    // 基础状态
    phone,
    code,
    isBound,
    codeSent,

    // 派生状态
    isPhoneValid,
    isCodeInputEnabled,
    isBindButtonEnabled,

    // 倒计时相关
    count,
    isRunning,
    startCountdown: start,

    // 状态更新函数
    setPhone,
    setCode,
    setCodeSentStatus,
    setBoundStatus,
    clearFormData,
    switchToBindPage,
  };
};
